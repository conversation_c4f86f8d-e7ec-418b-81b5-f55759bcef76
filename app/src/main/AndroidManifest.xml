<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.fenqi.main">
    <queries package="${applicationId}">
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE">

            </action>
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE">

            </action>
        </intent>
    </queries>

    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA"
        tools:ignore="PermissionImpliesUnsupportedChromeOsHardware" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <uses-permission android:name="android.permission.READ_CONTACTS"/>

    <application
        android:name="com.fenqi.main.MainApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@drawable/ymt"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/Theme.RetrofitRxJava"
        android:fullBackupContent="false"
        android:debuggable="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:label="@string/app_name"
        tools:replace="android:allowBackup"
        tools:ignore="AllowBackup,HardcodedDebugMode,UnusedAttribute">

        <activity android:name=".page.splash.SplashActivity"
            android:launchMode="singleTop"
            android:exported="true"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name="com.fenqi.main.page.main.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>

        <activity android:name="com.fenqi.main.page.apply.ApplyNowActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.authinfo.bindbank.cardlist.BankCardListActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.verifycode.VerifyCodeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.authinfo.personal.PersonalInfoActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustResize"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.authinfo.emergencycontact.EmergencyContactActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustResize"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>

        <activity android:name="com.fenqi.main.page.selecter.SelecterActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.authinfo.faceocr.FaceOcrActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.webprivacy.WebPrivacyActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name=".page.payapp.PayAppActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.paidlist.PaidListActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.login.LoginActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>

        <activity android:name="com.fenqi.main.page.setting.SettingActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.web.CommonWebActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>
        <activity android:name="com.fenqi.main.page.authinfo.bindbank.BindBankActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
        </activity>


        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/app_path"/>
        </provider>
    </application>

</manifest>